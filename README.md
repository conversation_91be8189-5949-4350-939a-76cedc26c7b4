# Hennessy RPA System

Robotic process automation system for Hennessy Automotive that automates invoice processing, email handling, and dealer system integration.

## Overview

This system automates:
- **Invoice Downloads**: From Ford, Lexus, JLR, Porsche, Cadillac, Honda, Mazda dealer portals
- **Email Processing**: Automated email monitoring and document processing
- **Document Management**: Bills of Lading, invoices, and title documents
- **Reynolds Integration**: Vehicle data management in Reynolds system

## Project Structure

```
hennessy/
├── hennessy/                   # Core RPA automation (Python/Selenium)
├── hennessy-aria/             # Serverless email processing (AWS Lambda)
└── hennessy-rpa-reynolds/     # Reynolds system integration (Python RPA)
```

## Quick Start

### Prerequisites

- Python 3.11+
- Node.js 16+
- Docker
- AWS CLI configured
- MongoDB access

### Installation

```bash
# Clone repository
git clone <repository-url>
cd hennessy

# Install core RPA dependencies
cd hennessy
pip install -r requirements.txt

# Install serverless dependencies
cd ../hennessy-aria
npm install
npm install -g serverless@3

# Install Reynolds integration dependencies
cd ../hennessy-rpa-reynolds
pip install -r requirements.txt
```

### Configuration

1. **Environment Variables** (see `.env` files in each directory):
   ```bash
   ENV=snd-hen                    # Environment stage
   AWS_REGION=us-east-1          # AWS region
   ```

2. **AWS Secrets Manager** - Configure these secrets:
   - `{ENV}-mongodb_uri` - Database connection
   - `{ENV}-email_credentials` - Email authentication
   - `{ENV}-user_login_{store}` - Dealer portal credentials (FOR, LOA, JLRN, etc.)

### Local Development

```bash
# Start LocalStack for local testing
pip install localstack
localstack start

# Deploy serverless functions locally
cd hennessy-aria
npm run deploy:local
```

## Usage

### Invoice Download
```python
# Example Lambda event
{
    "store": "FOR",
    "action": "invoice_download",
    "stage": "post-inventory",
    "data": {"vins": ["1FTFW1ET5DFC12345"]}
}
```

### Email Processing
```bash
cd hennessy
python process_emails.py --count 50 --output ./output
```

### Reynolds Integration
```bash
cd hennessy-rpa-reynolds
python insert_vehicle_v2.py
```

## Deployment

### Core RPA (Docker)
```bash
cd hennessy
./build_deploy.sh
```

### Serverless Functions
```bash
cd hennessy-aria
npm run deploy:snd    # Deploy to sandbox
npm run deploy:prd    # Deploy to production
```

## Testing

```bash
# Unit tests
cd hennessy
pytest

# Test Lambda functions
cd hennessy-aria
serverless invoke --stage local --function email_watcher
```

## Troubleshooting

### Common Issues
- **Chrome/Driver mismatch**: Rebuild Docker image
- **MongoDB connection**: Check URI in Secrets Manager
- **Email auth**: Verify Azure AD credentials

### Debug Mode
```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Support

- **Development**: <EMAIL>, <EMAIL>
- **Operations**: <EMAIL>
- **ARIA Support**: <EMAIL>
