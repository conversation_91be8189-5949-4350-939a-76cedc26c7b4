# Hennessy RPA System

Robotic process automation system for Hennessy Automotive that automates invoice processing, email handling, and dealer system integration.

## Overview

This system automates:
- **Invoice Downloads**: From Ford, Lexus, JLR, Porsche, Cadillac, Honda, Mazda dealer portals
- **Email Processing**: Automated email monitoring and document processing
- **Document Management**: Bills of Lading, invoices, and title documents
- **Reynolds Integration**: Vehicle data management in Reynolds system

## Project Structure

```
hennessy/
├── hennessy/                   # Core RPA automation (Python/Selenium)
├── hennessy-aria/             # Serverless email processing (AWS Lambda)
└── hennessy-rpa-reynolds/     # Reynolds system integration (Python RPA)
```

## Quick Start

### Prerequisites

- Python 3.11+
- Node.js 16+
- Docker
- <PERSON>WS CLI configured
- MongoDB access

### Installation

```bash
git clone <repository-url>
cd hennessy

cd hennessy
pip install -r requirements.txt

cd ../hennessy-aria
npm install
npm install -g serverless@3

cd ../hennessy-rpa-reynolds
pip install -r requirements.txt
```

### Configuration

1. **Environment Variables** (see `.env` files in each directory):
   ```bash
   ENV=snd-hen
   AWS_REGION=us-east-1
   ```

2. **AWS Secrets Manager** - Configure these secrets:
   - `{ENV}-mongodb_uri`
   - `{ENV}-email_credentials`
   - `{ENV}-user_login_{store}`

### Local Development

```bash
pip install localstack
localstack start

cd hennessy-aria
npm run deploy:local
```

## Usage

### Invoice Download
```python
{
    "store": "FOR",
    "action": "invoice_download",
    "stage": "post-inventory",
    "data": {"vins": ["1FTFW1ET5DFC12345"]}
}
```

### Email Processing
```bash
cd hennessy
python process_emails.py --count 50 --output ./output
```

### Reynolds Integration
```bash
cd hennessy-rpa-reynolds
python insert_vehicle_v2.py
```

## Deployment

### Core RPA (Docker)
```bash
cd hennessy
./build_deploy.sh
```

### Serverless Functions
```bash
cd hennessy-aria
npm run deploy:snd
npm run deploy:prd
```

## Testing

```bash
cd hennessy
pytest

cd hennessy-aria
serverless invoke --stage local --function email_watcher
```

## Troubleshooting

### Common Issues
- **Chrome/Driver mismatch**: Rebuild Docker image
- **MongoDB connection**: Check URI in Secrets Manager
- **Email auth**: Verify Azure AD credentials

### Debug Mode
```bash
DEBUG=true
LOG_LEVEL=DEBUG
```
