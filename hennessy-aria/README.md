# Hennessy ARIA - Serverless Email Processing

AWS Lambda-based system for processing emails, extracting documents, and creating ARIA work items.

## Overview

This serverless application:
- **Monitors Emails**: Watches inbox for new emails with attachments
- **Processes Documents**: Extracts data from invoices, BOLs, and titles
- **Creates Work Items**: Automatically creates ARIA work items
- **Orchestrates Workflows**: Uses Step Functions for complex processing

## Prerequisites

- Node.js 16+
- AWS CLI configured
- Serverless Framework v3

## Installation

```bash
npm install
npm install -g serverless@3
```

## Configuration

### Environment Stages

- **local** - LocalStack development
- **snd-hen** - Sandbox environment
- **prd-hen** - Production environment

### AWS Secrets Manager

Configure these secrets for each environment:

```json
// {ENV}-mongodb_uri
"********************************:port/database"

// {ENV}-email_credentials
{
  "mfa_outlook_config": {
    "client_id": "azure-app-client-id",
    "client_secret": "azure-app-client-secret",
    "tenant_id": "azure-tenant-id",
    "user_id": "<EMAIL>"
  }
}

// {ENV}-llm_params
{
  "openai": {...},
  "bedrock": {...}
}
```

## Local Development

### Using LocalStack

```bash
pip install localstack
localstack start

aws s3 mb s3://ach-deployment-bucket-local --endpoint-url=http://localhost:4566

npm run deploy:local
```

### Test Functions

```bash
serverless invoke --stage local --function email_watcher

serverless invoke --stage local --function process_email --data '{
  "email_id": "test-id",
  "stage": "post-inventory"
}'
```

## Deployment

```bash
npm run deploy:snd
npm run deploy:prd
npm run deploy:snd:function --function=email_watcher
npm run deploy:snd:stepfunction --function=process_emails
```

## Project Structure

```
hennessy-aria/
├── src/                       # Lambda function source code
│   ├── email_watcher/        # Email monitoring
│   ├── process_email/        # Email processing
│   ├── llm_extractor/        # AI data extraction
│   ├── bre/                  # Business rules engine
│   └── report_to_aria/       # ARIA integration
├── stepfunctions/            # Step Function workflows
├── resources/                # CloudFormation resources
├── layers/                   # Lambda layers
└── scripts/                  # Deployment scripts
```

## Key Lambda Functions

### Core Functions
- **email_watcher** - Monitors email accounts
- **process_email** - Processes emails and attachments
- **llm_extractor** - AI-powered data extraction
- **bre** - Business rules engine
- **report_to_aria** - Creates ARIA work items

### Utility Functions
- **move_email** - Moves processed emails
- **pdf_utils** - PDF processing
- **error_wi_report** - Error reporting

## Step Functions

### Email Processing Workflow
1. Email Watcher monitors inbox
2. Process Email handles attachments
3. LLM Extractor extracts data
4. BRE applies business rules
5. Report to ARIA creates work items

## Testing

```bash
serverless invoke --stage local --function email_watcher

aws stepfunctions start-execution \
  --state-machine-arn "arn:aws:states:region:account:stateMachine:local-process_emails" \
  --input '{"stage": "post-inventory"}'
```

## Monitoring

### CloudWatch Logs
- `/aws/lambda/{ENV}-email_watcher`
- `/aws/lambda/{ENV}-process_email`
- `/aws/lambda/{ENV}-llm_extractor`

### Error Handling
- Comprehensive error logging
- Email notifications for failures
- Retry mechanisms for transient errors

## Troubleshooting

### Common Issues

1. **Deployment failures**:
   ```bash
   serverless remove --stage local
   npm run deploy:local
   ```

2. **LocalStack issues**:
   ```bash
   localstack stop
   docker system prune -f
   localstack start
   ```

3. **Permission errors**:
   - Check IAM roles in `resources/roles/`
   - Verify Secrets Manager access

### Debug Mode
Set environment variables in function configuration:
```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Development

### Adding New Function
1. Create function in `src/new_function/`
2. Add configuration to `resources/functions.{stage}.yml`
3. Deploy with `npm run deploy:snd:function --function=new_function`

### Environment Variables
Lambda environment variables are set in `resources/functions.{stage}.yml` files and deployed automatically.