# Hennessy RPA Reynolds Integration

Python RPA automation for integrating with Reynolds and Reynolds dealer management system.

## Overview

This module handles:
- **Vehicle Data Management**: Insert and update vehicle information in Reynolds
- **Pre-Inventory Processing**: Handle pre-inventory vehicle data
- **Used Car Management**: Process used car inventory data
- **Title Processing**: Manage vehicle title information
- **Password Management**: Automated password reset for Reynolds accounts

## Prerequisites

- Python 3.8+
- Windows OS (required for pywin32 dependency)
- RPA Framework (TagUI)
- Reynolds database access

## Installation

```bash
pip install -r requirements.txt
```

## Configuration

### Environment Variables

Create `.env` file with required configuration:

```bash
LOG_LEVEL=INFO
LOG_PATH=./logs/
```

## Core Scripts

### Vehicle Processing Scripts

```bash
python insert_vehicle_v2.py
python insert_pre_inventory_car.py
python insert_used_car.py
python insert_title.py
python reset_reynolds_password.py
```

## Project Structure

```
hennessy-rpa-reynolds/
├── HEN_Utilities/              # Hennessy-specific utilities
├── Process_Utils/              # Process automation utilities
├── Utilities/
│   └── Common_Utilities/
│       ├── logger_utility.py   # Logging functionality
│       └── env_file_utility.py # Environment file handling
├── insert_*.py                 # Main processing scripts
└── requirements.txt           # Python dependencies
```

## Utilities

### Logger Utility

```python
from Utilities.Common_Utilities.logger_utility import Logger

logger = Logger('./logs/')
logger.info("Information message")
logger.error("Error message")
```

### Environment File Utility

```python
from Utilities.Common_Utilities.env_file_utility import Env_File

env = Env_File('./logs/', '.env')
values = env.get_values()
config_value = values.get('CONFIG_KEY')
```

## Dependencies

Key dependencies from `requirements.txt`:
- `rpa==1.50.0` - Core RPA automation framework
- `tagui==1.50.0` - TagUI automation library
- `pyautogui` - GUI automation
- `pywin32` - Windows-specific operations
- `pandas` - Data processing
- `python-dotenv` - Environment variable management

## Troubleshooting

### Common Issues

1. **RPA Framework Issues**:
   ```bash
   pip uninstall rpa tagui
   pip install rpa==1.50.0 tagui==1.50.0
   ```

2. **Windows Issues**:
   ```bash
   pip uninstall pywin32
   pip install pywin32
   ```

3. **Database Connection**:
   - Verify database server accessibility
   - Check credentials and permissions
   - Ensure ODBC drivers are installed

### Debug Mode

```bash
LOG_LEVEL=DEBUG
```

### Log Analysis

```bash
tail -f logs/rpa_operations.log
grep "ERROR" logs/rpa_operations.log
```

## Development

### Adding New Scripts

1. Create new Python script in root directory
2. Use logger utility for logging
3. Use env_file utility for configuration
4. Follow existing script patterns

### Configuration Management

The `env_file_utility.py` reads configuration from `.env` files using `dotenv_values()`. Add configuration variables to `.env` as needed by your specific scripts.
