# Hennessy Core RPA Module

Core automation engine for downloading invoices from dealer portals and processing documents.

## Overview

This module handles:
- **Invoice Downloads**: Automated downloads from Ford, Lexus, JLR, Porsche, Cadillac, Honda, Mazda portals
- **Document Processing**: Handles invoices, Bills of Lading, and title documents
- **Selenium Automation**: Web scraping and portal automation
- **AWS Integration**: Lambda deployment with Docker containers

## Supported Dealer Systems

- **Ford** (FOR) - Ford dealer portal
- **Lexus** (LOA, LOG) - Lexus Atlanta and Gwinnett
- **JLR** (JLRN, JLRB, JLRG) - Jaguar Land Rover systems
- **Porsche** (POR, PNW) - Porsche and Porsche Northwest
- **Cadillac** (CAD) - Cadillac dealer system
- **Honda** (HON) - Honda dealer portal with OTP
- **Mazda** (MBG) - Mazda dealer system
- **Manheim** - Auction platform

## Prerequisites

- Python 3.11+
- Docker (for Lambda deployment)
- Chrome/Chromium (for Selenium)
- AWS CLI configured

## Installation

```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

## Configuration

### Environment Variables

Set in `hennessy/.env`:
```bash
ENV=snd-hen
AWS_REGION=us-east-1
AWS_ACCOUNT_ID=<account-id>
```

### AWS Secrets Manager

Configure these secrets:
- `{ENV}-selenium_config`
- `{ENV}-mongodb_uri`
- `{ENV}-email_credentials`
- `{ENV}-user_login_{store}`

Example dealer credentials:
```json
{
  "user": "dealer_username",
  "actual_password": "dealer_password"
}
```

## Usage

### Invoice Download

```python
{
    "store": "FOR",
    "action": "invoice_download",
    "stage": "post-inventory",
    "data": {
        "vins": ["1FTFW1ET5DFC12345"]
    }
}
```

### Email Processing

```bash
python process_emails.py --count 50 --output ./output
```

### Supported Actions

- `invoice_download` - Download invoices for VINs
- `download_honda_pricing_guide` - Honda pricing guides
- `download_new_vehicles_report` - New vehicle reports
- `reset_password` - Reset dealer portal passwords

## Deployment

### Docker Lambda Deployment

```bash
chmod +x build_deploy.sh
./build_deploy.sh
```

This script:
1. Builds Docker image with Chrome and dependencies
2. Pushes to AWS ECR
3. Updates/creates Lambda function

## Project Structure

```
hennessy/
├── app.py                     # Main Lambda handler
├── process_emails.py          # Email processing utility
├── Invoices/
│   ├── config.py             # Configuration management
│   └── vendors/              # Dealer-specific automation
├── invokeRPA/                # RPA utilities and drivers
├── Dockerfile                # Docker container definition
└── build_deploy.sh          # Deployment script
```

## Testing

```bash
pytest
pytest --cov=.
python app.py
```

## Troubleshooting

### Common Issues

1. **Chrome/Driver mismatch**:
   ```bash
   docker build --no-cache -t pyautomationaws/selenium .
   ```

2. **Dealer login failures**:
   - Check credentials in Secrets Manager
   - Verify OTP configuration for Honda
   - Check for password expiration

3. **MongoDB connection**:
   - Verify URI in Secrets Manager
   - Check network connectivity

### Debug Mode

```bash
DEBUG=true
LOG_LEVEL=DEBUG
```

## Development

### Adding New Dealer

1. Create vendor class in `Invoices/vendors/`
2. Add store code to `app.py`
3. Configure credentials in Secrets Manager
4. Test with sample VINs

### Code Quality

```bash
pre-commit install
ruff check .
```


